"""FastAPI main application entry point for trading architecture backend."""
import logging
from trading_environment.backend.config.settings import settings
from mattlibrary.logging.logging_config import setup_logging

# Setup logging
setup_logging(
    logging_enabled=settings.logging_enabled, 
    log_level=settings.log_level, 
    log_to_console=settings.console_logging_enabled,
    log_to_file=settings.file_logging_enabled,
    log_file=settings.log_file, 
    clean_log_file=settings.clean_log_file
)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from trading_environment.backend.app.manual_trading.router import router as manual_trading_router

# Create FastAPI app
app = FastAPI(
    title="Trading Architecture Backend",
    description="Manual Trading Simulator with FastAPI",
    version="1.0.0"
)

# Add CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include manual trading router
app.include_router(manual_trading_router, prefix="/api/v1/manual-trading", tags=["Manual Trading"])

@app.get("/")
async def root():
    """Root endpoint for health check."""
    return {"message": "Trading Architecture Backend is running"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}