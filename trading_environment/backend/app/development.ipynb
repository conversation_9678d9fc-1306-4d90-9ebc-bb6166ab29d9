%reload_ext autoreload
%autoreload 2

import os
import sys
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

#config for jupyter cell output
# import polars as pl
# pl.Config.set_tbl_rows(20)

#add project root to sys.path
sys.path.append(os.path.join(os.getcwd(), '../../..'))

from mattlibrary.logging.logging_config import setup_logging
from mattlibrary.trading.trading_engine import TradingEngine
from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI
from mattlibrary.trading.symbol import Symbol, Forex, Stock
from mattlibrary.trading.parent_orders.parent_order_standard import ParentOrderStandard
from mattlibrary.trading.child_order_type import ChildOrderType
from trading_environment.backend.config.settings import settings

#logging config
setup_logging(True, "INFO", True, True, log_file="development.log", clean_log_file=True)

#trading engine
trading_engine = TradingEngine(
        settings.starting_balance, 
        settings.base_currency, 
        settings.track_performance, 
        settings.execution_plugin_type,
        settings.static_data_directory,
        settings.read_positions_from_disk,
        settings.write_positions_to_disk
    )

parent_order = ParentOrderStandard(
    strategy_id="test",
    symbol_id="FOREX_USDJPY",
    size=100_000,
    order_type=ChildOrderType.MARKET
)

trading_engine.submit_parent_order(parent_order)

#market data
ib_api = InteractiveBrokersAPI(settings.ib_address, settings.ib_port, settings.ib_client_id, True)
ib_api.connect()

#fetch market data
symbol = Forex("USDJPY")
start_dt = None
end_dt = datetime.now(ZoneInfo("Pacific/Auckland"))
number_ticks = 1
what_to_show = "BID_ASK"
use_rth_only = True
ignore_update_size = True

#fetch market data update
data = ib_api.get_historical_ticks(symbol, start_dt, end_dt, number_ticks, what_to_show, use_rth_only, ignore_update_size)[-1]

#disconnect
ib_api.disconnect()

market_update = dict(
    symbol=symbol.symbolId,
    datetime=data.timestamp,
    bid=data.bid,
    ask=data.ask
)

trading_engine.process_market_data(market_update)

current_positions = trading_engine.get_current_positions()
current_positions